/**
 * Middleware to capture pLevel parameter and store it in request context
 */

export default () => {
  return async (ctx: any, next: any) => {
    // Extract pLevel from query parameters if present
    if (ctx.query && ctx.query.pLevel !== undefined) {
      const pLevel = parseInt(ctx.query.pLevel as string, 10);
      if (!isNaN(pLevel)) {
        ctx.state.pLevel = pLevel;
        console.log('Middleware - pLevel captured:', pLevel);
      }
    }
    
    await next();
  };
};
