/**
 * content-crm controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::content-crm.content-crm', ({ strapi }) => ({
  async find(ctx) {
    // Extract pLevel from query parameters
    const { pLevel, ...restQuery } = ctx.query;

    // Store pLevel in request context so it can be accessed in lifecycle events
    if (pLevel !== undefined) {
      ctx.state.pLevel = parseInt(pLevel as string, 10);
    }

    // Call the default find method with the rest of the query parameters
    ctx.query = restQuery;
    return await super.find(ctx);
  },

  async findOne(ctx) {
    // Extract pLevel from query parameters
    const { pLevel, ...restQuery } = ctx.query;

    // Store pLevel in request context so it can be accessed in lifecycle events
    if (pLevel !== undefined) {
      ctx.state.pLevel = parseInt(pLevel as string, 10);
    }

    // Call the default findOne method with the rest of the query parameters
    ctx.query = restQuery;
    return await super.findOne(ctx);
  }
}));
